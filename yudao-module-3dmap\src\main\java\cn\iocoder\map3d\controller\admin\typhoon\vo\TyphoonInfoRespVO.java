package cn.iocoder.map3d.controller.admin.typhoon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 台风基本信息响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 台风基本信息响应 VO")
@Data
public class TyphoonInfoRespVO {

    @Schema(description = "台风ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "英文名称", example = "HAIKUI")
    private String englishName;

    @Schema(description = "中文名称", example = "海葵")
    private String chineseName;

    @Schema(description = "台风编号", example = "202311")
    private String typhoonNumber;

    @Schema(description = "台风代码", example = "1311")
    private String typhoonCode;

    @Schema(description = "参考ID", example = "2023")
    private Long referenceId;

    @Schema(description = "名称含义/来源说明", example = "海葵是一种海洋生物")
    private String meaning;

    @Schema(description = "状态", example = "start")
    private String status;

    @Schema(description = "年份", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023")
    private Integer year;

}
