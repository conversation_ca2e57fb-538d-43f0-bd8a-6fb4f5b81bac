package cn.iocoder.map3d.controller.admin.typhoon;

import cn.iocoder.map3d.controller.admin.typhoon.vo.TyphoonDetailRespVO;
import cn.iocoder.map3d.controller.admin.typhoon.vo.TyphoonInfoRespVO;
import cn.iocoder.map3d.controller.admin.typhoon.vo.TyphoonTrackRespVO;
import cn.iocoder.map3d.dal.dataobject.TyphoonInfo;
import cn.iocoder.map3d.dal.dataobject.TyphoonTrack;
import cn.iocoder.map3d.service.TyphoonInfoService;
import cn.iocoder.map3d.service.TyphoonTrackService;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 台风信息 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 台风信息")
@RestController
@RequestMapping("/3dmap/typhoon")
@Validated
public class TyphoonController {

    @Resource
    private TyphoonInfoService typhoonInfoService;

    @Resource
    private TyphoonTrackService typhoonTrackService;

    @GetMapping("/info/list-by-year")
    @Operation(summary = "根据年份查询台风基本信息列表")
    @Parameter(name = "year", description = "年份", required = true, example = "2024")
    public CommonResult<List<TyphoonInfoRespVO>> getTyphoonInfoListByYear(@RequestParam("year") Integer year) {
        List<TyphoonInfo> list = typhoonInfoService.getTyphoonInfoListByYear(year);
        return success(BeanUtils.toBean(list, TyphoonInfoRespVO.class));
    }

    @GetMapping("/track/list-by-typhoon-id")
    @Operation(summary = "根据台风ID查询台风轨迹坐标列表")
    @Parameter(name = "typhoonId", description = "台风ID", required = true, example = "1")
    public CommonResult<List<TyphoonTrackRespVO>> getTyphoonTrackListByTyphoonId(@RequestParam("typhoonId") Long typhoonId) {
        List<TyphoonTrack> list = typhoonTrackService.getTyphoonTrackListByTyphoonId(typhoonId);
        return success(BeanUtils.toBean(list, TyphoonTrackRespVO.class));
    }

    @GetMapping("/info/get")
    @Operation(summary = "获得台风基本信息")
    @Parameter(name = "id", description = "台风ID", required = true, example = "1")
    public CommonResult<TyphoonInfoRespVO> getTyphoonInfo(@RequestParam("id") Long id) {
        TyphoonInfo typhoonInfo = typhoonInfoService.getById(id);
        return success(BeanUtils.toBean(typhoonInfo, TyphoonInfoRespVO.class));
    }

    @GetMapping("/detail/get")
    @Operation(summary = "获得台风详细信息（包含基本信息和轨迹数据）")
    @Parameter(name = "id", description = "台风ID", required = true, example = "1")
    public CommonResult<TyphoonDetailRespVO> getTyphoonDetail(@RequestParam("id") Long id) {
        // 获取台风基本信息
        TyphoonInfo typhoonInfo = typhoonInfoService.getById(id);
        if (typhoonInfo == null) {
            return success(null);
        }

        // 获取台风轨迹数据
        List<TyphoonTrack> trackList = typhoonTrackService.getTyphoonTrackListByTyphoonId(id);

        // 组装响应数据
        TyphoonDetailRespVO result = new TyphoonDetailRespVO();
        result.setTyphoonInfo(BeanUtils.toBean(typhoonInfo, TyphoonInfoRespVO.class));
        result.setTrackList(BeanUtils.toBean(trackList, TyphoonTrackRespVO.class));

        return success(result);
    }

}
