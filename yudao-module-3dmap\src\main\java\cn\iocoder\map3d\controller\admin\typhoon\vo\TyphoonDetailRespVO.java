package cn.iocoder.map3d.controller.admin.typhoon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 台风详细信息响应 VO（包含基本信息和轨迹数据）
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 台风详细信息响应 VO")
@Data
public class TyphoonDetailRespVO {

    @Schema(description = "台风基本信息")
    private TyphoonInfoRespVO typhoonInfo;

    @Schema(description = "台风轨迹坐标列表")
    private List<TyphoonTrackRespVO> trackList;

}
