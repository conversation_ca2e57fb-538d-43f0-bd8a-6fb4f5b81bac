package cn.iocoder.map3d.controller.admin.typhoon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 台风轨迹坐标响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 台风轨迹坐标响应 VO")
@Data
public class TyphoonTrackRespVO {

    @Schema(description = "轨迹ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "台风ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long typhoonId;

    @Schema(description = "轨迹点ID", example = "1")
    private Long trackId;

    @Schema(description = "时间字符串", example = "2023-09-05 08:00")
    private String timeStr;

    @Schema(description = "时间戳(毫秒)", example = "1693872000000")
    private Long timestampMs;

    @Schema(description = "台风等级", example = "台风")
    private String level;

    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED, example = "121.5")
    private BigDecimal longitude;

    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED, example = "25.2")
    private BigDecimal latitude;

    @Schema(description = "气压(hPa)", example = "980")
    private Integer pressure;

    @Schema(description = "风速(m/s)", example = "35")
    private Integer windSpeed;

    @Schema(description = "移动方向", example = "西北")
    private String direction;

    @Schema(description = "移动速度(km/h)", example = "15")
    private Integer speed;

}
